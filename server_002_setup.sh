#!/bin/bash
# Server_002 Setup: Kamailio SIP Proxy + Load Balancer + PostgreSQL Database
# FIXED VERSION - Based on FreeSWITCH documentation best practices

echo "=== Server_002 Setup: Kamailio + PostgreSQL (FIXED) ==="
echo "Purpose: SIP Proxy, Load Balancer, and Centralized Database"
echo ""

# Configuration variables
SERVER_002_PUBLIC_IP="***********"  # Public IP for external reference
SERVER_003_IP="***********"  # FreeSWITCH Server 1
SERVER_004_IP="*************" # FreeSWITCH Server 2
SIP_DOMAIN="km1.ethiopiatrips.com"
PG_DB="freeswitch_db"
PG_USER="fs_user"
PG_PASS="SecurePass2024!"

# Auto-detect the correct IP to bind to
# On AWS EC2, we need to bind to the private IP, not the public IP
if curl -s --max-time 5 http://***************/latest/meta-data/local-ipv4 &>/dev/null; then
    # We're on AWS EC2, use the private IP
    SERVER_002_IP=$(curl -s http://***************/latest/meta-data/local-ipv4)
    echo "Detected AWS EC2 environment, using private IP: $SERVER_002_IP"
else
    # Not on AWS, try to detect the main network interface IP
    SERVER_002_IP=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null)
    if [ -z "$SERVER_002_IP" ]; then
        # Fallback to localhost if we can't detect
        SERVER_002_IP="0.0.0.0"
        echo "Could not detect IP, binding to all interfaces (0.0.0.0)"
    else
        echo "Detected local IP: $SERVER_002_IP"
    fi
fi

echo "Configuration:"
echo "  Server Bind IP: $SERVER_002_IP"
echo "  Server Public IP: $SERVER_002_PUBLIC_IP"
echo "  SIP Domain: $SIP_DOMAIN"
echo "  Database: $PG_DB"
echo "  FreeSWITCH Servers: $SERVER_003_IP, $SERVER_004_IP"
echo ""

echo "1. Stopping any existing Kamailio service..."
sudo systemctl stop kamailio 2>/dev/null || true
echo ""

echo "2. Cleaning up broken repositories..."
# Remove any broken Kamailio repositories
sudo rm -f /etc/apt/sources.list.d/kamailio* 2>/dev/null || true
sudo rm -f /etc/apt/sources.list.d/*kamailio* 2>/dev/null || true

# Remove any broken repository keys
sudo apt-key del 0xFB40D3E6508EA4C8 2>/dev/null || true

echo "✓ Cleaned up old repository configurations"
echo ""

echo "3. Updating system packages..."
sudo apt update && sudo apt upgrade -y
echo ""

echo "4. Installing PostgreSQL 15..."
sudo apt install -y postgresql postgresql-contrib postgresql-client
sudo <NAME_EMAIL>
sudo <NAME_EMAIL>
echo ""

echo "5. Configuring PostgreSQL..."
# Create database and user with proper permissions
sudo -u postgres psql << EOF
-- Drop and recreate database for clean setup
DROP DATABASE IF EXISTS $PG_DB;
DROP USER IF EXISTS $PG_USER;

CREATE DATABASE $PG_DB;
CREATE USER $PG_USER WITH PASSWORD '$PG_PASS';
GRANT ALL PRIVILEGES ON DATABASE $PG_DB TO $PG_USER;
ALTER USER $PG_USER CREATEDB;
ALTER USER $PG_USER CREATEROLE;

-- Connect to the database and grant schema permissions
\c $PG_DB
GRANT ALL ON SCHEMA public TO $PG_USER;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $PG_USER;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $PG_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $PG_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $PG_USER;
ALTER SCHEMA public OWNER TO $PG_USER;
-- Make user superuser temporarily to ensure table creation works
ALTER USER $PG_USER WITH SUPERUSER;
\q
EOF

# Configure PostgreSQL for network access
PG_CONFIG="/etc/postgresql/15/main"
sudo cp "$PG_CONFIG/postgresql.conf" "$PG_CONFIG/postgresql.conf.backup.$(date +%Y%m%d_%H%M%S)"
sudo cp "$PG_CONFIG/pg_hba.conf" "$PG_CONFIG/pg_hba.conf.backup.$(date +%Y%m%d_%H%M%S)"

sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/" "$PG_CONFIG/postgresql.conf"
sudo sed -i "s/#port = 5432/port = 5432/" "$PG_CONFIG/postgresql.conf"

# Add authentication rules
echo "# Kamailio access" | sudo tee -a "$PG_CONFIG/pg_hba.conf"
echo "local   $PG_DB    $PG_USER                    md5" | sudo tee -a "$PG_CONFIG/pg_hba.conf"
echo "host    $PG_DB    $PG_USER    127.0.0.1/32   md5" | sudo tee -a "$PG_CONFIG/pg_hba.conf"
echo "host    $PG_DB    $PG_USER    localhost      md5" | sudo tee -a "$PG_CONFIG/pg_hba.conf"

sudo <NAME_EMAIL>
echo "✓ PostgreSQL configured"
echo ""

echo "6. Installing Kamailio from official Debian repository..."
# Install Kamailio from the official Debian repository
sudo apt install -y kamailio kamailio-postgres-modules kamailio-utils-modules

# Verify installation
if dpkg -l | grep -q kamailio; then
    echo "✓ Kamailio installed successfully"
    kamailio -v
else
    echo "✗ Kamailio installation failed"
    exit 1
fi
echo ""

echo "7. Creating Kamailio database schema using kamdbctl..."
# Configure kamdbctl for PostgreSQL
sudo tee /etc/kamailio/kamctlrc << EOF
# Database engine
DBENGINE=POSTGRESQL

# Database host
DBHOST=localhost

# Database name
DBNAME=$PG_DB

# Database read/write user
DBRWUSER=$PG_USER

# Database password
DBRWPW=$PG_PASS

# Database root user (for schema creation)
DBROOTUSER=$PG_USER

# Database root password
DBROOTPW=$PG_PASS

# Database port
DBPORT=5432

# Path to psql
PSQL=/usr/bin/psql

# Charset
CHARSET=utf8
EOF

echo "✓ kamctlrc configured"

# Create Kamailio database schema using our user (not postgres)
export PGPASSWORD="$PG_PASS"
echo "Creating Kamailio database schema..."

# Check if tables already exist
TABLE_COUNT=$(psql -h localhost -U $PG_USER -d $PG_DB -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('subscriber', 'location', 'dispatcher');" 2>/dev/null | tr -d ' ')

if [ "$TABLE_COUNT" = "3" ]; then
    echo "✓ Kamailio tables already exist"
else
    echo "Creating Kamailio database tables..."
    # Drop the database and recreate it to ensure clean schema creation
    sudo -u postgres psql << EOF
DROP DATABASE IF EXISTS $PG_DB;
CREATE DATABASE $PG_DB;
GRANT ALL PRIVILEGES ON DATABASE $PG_DB TO $PG_USER;
\c $PG_DB
GRANT ALL ON SCHEMA public TO $PG_USER;
ALTER SCHEMA public OWNER TO $PG_USER;
ALTER USER $PG_USER WITH SUPERUSER;
\q
EOF

    # Now create the schema with kamdbctl
    sudo -E kamdbctl create << EOF
y
y
EOF

    if [ $? -eq 0 ]; then
        echo "✓ Kamailio database schema created successfully"
    else
        echo "✗ Kamailio database schema creation failed"
        echo "Attempting manual table creation..."

        # Manual table creation as fallback
        psql -h localhost -U $PG_USER -d $PG_DB << 'EOSQL'
-- Create basic Kamailio tables manually
CREATE TABLE IF NOT EXISTS version (
    table_name VARCHAR(32) NOT NULL,
    table_version INTEGER DEFAULT 0 NOT NULL,
    CONSTRAINT version_table_name_idx PRIMARY KEY (table_name)
);

CREATE TABLE IF NOT EXISTS subscriber (
    id SERIAL PRIMARY KEY,
    username VARCHAR(64) DEFAULT '' NOT NULL,
    domain VARCHAR(64) DEFAULT '' NOT NULL,
    password VARCHAR(25) DEFAULT '' NOT NULL,
    email_address VARCHAR(64) DEFAULT '' NOT NULL,
    ha1 VARCHAR(64) DEFAULT '' NOT NULL,
    ha1b VARCHAR(64) DEFAULT '' NOT NULL,
    rpid VARCHAR(64) DEFAULT NULL,
    CONSTRAINT subscriber_account_idx UNIQUE (username, domain)
);

CREATE TABLE IF NOT EXISTS location (
    id SERIAL PRIMARY KEY,
    ruid VARCHAR(64) DEFAULT '' NOT NULL,
    username VARCHAR(64) DEFAULT '' NOT NULL,
    domain VARCHAR(64) DEFAULT NULL,
    contact VARCHAR(512) DEFAULT '' NOT NULL,
    received VARCHAR(128) DEFAULT NULL,
    path VARCHAR(512) DEFAULT NULL,
    expires TIMESTAMP WITHOUT TIME ZONE DEFAULT '2030-05-28 21:32:15' NOT NULL,
    q REAL DEFAULT 1.0 NOT NULL,
    callid VARCHAR(255) DEFAULT 'Default-Call-ID' NOT NULL,
    cseq INTEGER DEFAULT 1 NOT NULL,
    last_modified TIMESTAMP WITHOUT TIME ZONE DEFAULT '2000-01-01 00:00:01' NOT NULL,
    flags INTEGER DEFAULT 0 NOT NULL,
    cflags INTEGER DEFAULT 0 NOT NULL,
    user_agent VARCHAR(255) DEFAULT '' NOT NULL,
    socket VARCHAR(64) DEFAULT NULL,
    methods INTEGER DEFAULT NULL,
    instance VARCHAR(255) DEFAULT NULL,
    reg_id INTEGER DEFAULT 0 NOT NULL,
    server_id INTEGER DEFAULT 0 NOT NULL,
    connection_id INTEGER DEFAULT 0 NOT NULL,
    keepalive INTEGER DEFAULT 0 NOT NULL,
    partition INTEGER DEFAULT 0 NOT NULL
);

CREATE TABLE IF NOT EXISTS dispatcher (
    id SERIAL PRIMARY KEY,
    setid INTEGER DEFAULT 0 NOT NULL,
    destination VARCHAR(192) DEFAULT '' NOT NULL,
    flags INTEGER DEFAULT 0 NOT NULL,
    priority INTEGER DEFAULT 0 NOT NULL,
    attrs VARCHAR(128) DEFAULT '' NOT NULL,
    description VARCHAR(64) DEFAULT '' NOT NULL
);

-- Insert version information
INSERT INTO version (table_name, table_version) VALUES
    ('subscriber', 7),
    ('location', 9),
    ('dispatcher', 4)
ON CONFLICT (table_name) DO UPDATE SET table_version = EXCLUDED.table_version;

EOSQL

        if [ $? -eq 0 ]; then
            echo "✓ Manual table creation successful"
        else
            echo "✗ Manual table creation failed"
            exit 1
        fi
    fi
fi
echo ""

echo "7.5. Adding test users and dispatcher destinations..."
# Add our test data to the existing Kamailio schema
export PGPASSWORD="$PG_PASS"

# Verify tables exist before inserting data
if psql -h localhost -U $PG_USER -d $PG_DB -c "\dt" | grep -q "subscriber"; then
    echo "✓ Tables exist, adding test data..."

    psql -h localhost -U $PG_USER -d $PG_DB << EOF
-- Insert test users with proper password hashing
INSERT INTO subscriber (username, domain, password) VALUES
    ('1001', '$SIP_DOMAIN', 'pass1001'),
    ('1002', '$SIP_DOMAIN', 'pass1002'),
    ('1003', '$SIP_DOMAIN', 'pass1003')
ON CONFLICT (username, domain) DO UPDATE SET password = EXCLUDED.password;

-- Insert FreeSWITCH servers for load balancing
DELETE FROM dispatcher WHERE setid = 1;
INSERT INTO dispatcher (setid, destination, flags, priority, description) VALUES
    (1, 'sip:$SERVER_003_IP:5060', 0, 1, 'FreeSWITCH Server 1'),
    (1, 'sip:$SERVER_004_IP:5060', 0, 1, 'FreeSWITCH Server 2');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS subscriber_username_domain_idx ON subscriber(username, domain);
CREATE INDEX IF NOT EXISTS location_username_domain_idx ON location(username, domain);
CREATE INDEX IF NOT EXISTS location_expires_idx ON location(expires);
CREATE INDEX IF NOT EXISTS dispatcher_setid_idx ON dispatcher(setid);

-- Verify tables and data
SELECT 'Subscribers:' as info;
SELECT username, domain FROM subscriber;
SELECT 'Dispatcher destinations:' as info;
SELECT setid, destination, description FROM dispatcher;
EOF

    if [ $? -eq 0 ]; then
        echo "✓ Test data added successfully"
    else
        echo "✗ Failed to add test data"
        exit 1
    fi
else
    echo "✗ Tables do not exist - schema creation failed"
    exit 1
fi
echo ""

echo "7.5. Removing superuser privileges for security..."
sudo -u postgres psql << EOF
-- Remove superuser privileges for security
ALTER USER $PG_USER WITH NOSUPERUSER;
\q
EOF
echo "✓ User privileges normalized"
echo ""

echo "8. Testing database connectivity..."
export PGPASSWORD="$PG_PASS"

# Test basic connection first
if psql -h localhost -U $PG_USER -d $PG_DB -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✓ Database connection successful"

    # Test table access
    if psql -h localhost -U $PG_USER -d $PG_DB -c "SELECT COUNT(*) FROM dispatcher;" > /dev/null 2>&1; then
        echo "✓ Database tables accessible"
        echo ""
        echo "Current dispatcher destinations:"
        psql -h localhost -U $PG_USER -d $PG_DB -c "SELECT setid, destination, description FROM dispatcher;"
        echo ""
        echo "Current subscribers:"
        psql -h localhost -U $PG_USER -d $PG_DB -c "SELECT username, domain FROM subscriber;"
    else
        echo "✗ Database tables not accessible"
        echo "Available tables:"
        psql -h localhost -U $PG_USER -d $PG_DB -c "\dt"
        exit 1
    fi
else
    echo "✗ Database connection failed"
    echo "Connection details:"
    echo "  Host: localhost"
    echo "  Port: 5432"
    echo "  Database: $PG_DB"
    echo "  User: $PG_USER"
    exit 1
fi
echo ""

echo "9. Configuring Kamailio..."
sudo mkdir -p /run/kamailio
sudo chown kamailio:kamailio /run/kamailio 2>/dev/null || sudo chown root:root /run/kamailio

# Create simplified, working Kamailio configuration (without dispatcher initially)
cat << EOF | sudo tee /etc/kamailio/kamailio.cfg
#!KAMAILIO

# Global parameters
debug=2
log_stderror=no
log_facility=LOG_LOCAL0
fork=yes
children=4

# Network settings
listen=udp:$SERVER_002_IP:5060
port=5060

# Module path
mpath="/usr/lib/x86_64-linux-gnu/kamailio/modules/"

# Load core modules first
loadmodule "jsonrpcs.so"
loadmodule "kex.so"
loadmodule "corex.so"
loadmodule "sl.so"
loadmodule "tm.so"
loadmodule "tmx.so"
loadmodule "rr.so"
loadmodule "pv.so"
loadmodule "maxfwd.so"
loadmodule "textops.so"
loadmodule "siputils.so"
loadmodule "xlog.so"
loadmodule "sanity.so"
loadmodule "ctl.so"
loadmodule "cfg_rpc.so"
loadmodule "cfgutils.so"

# Database modules
loadmodule "db_postgres.so"

# Authentication modules
loadmodule "usrloc.so"
loadmodule "registrar.so"
loadmodule "auth.so"
loadmodule "auth_db.so"

# Note: Dispatcher module disabled initially to get basic proxy working

# Module parameters
modparam("jsonrpcs", "fifo_name", "/run/kamailio/kamailio_rpc.fifo")
modparam("ctl", "binrpc", "unix:/run/kamailio/kamailio_ctl")

# Transaction module
modparam("tm", "fr_timer", 30000)
modparam("tm", "fr_inv_timer", 120000)

# User location module
modparam("usrloc", "db_url", "postgres://$PG_USER:$PG_PASS@localhost/$PG_DB")
modparam("usrloc", "db_mode", 2)
modparam("usrloc", "timer_interval", 60)

# Authentication module
modparam("auth_db", "db_url", "postgres://$PG_USER:$PG_PASS@localhost/$PG_DB")
modparam("auth_db", "user_column", "username")
modparam("auth_db", "domain_column", "domain")
modparam("auth_db", "password_column", "password")

# Note: Dispatcher module parameters removed for basic proxy setup

# Main routing logic
request_route {
    # Basic checks
    if (!mf_process_maxfwd_header("10")) {
        sl_send_reply("483", "Too Many Hops");
        exit;
    }

    if (!sanity_check("1511", "7")) {
        xlog("L_WARN", "Malformed SIP message from \$si:\$sp\\n");
        exit;
    }

    # Handle CANCEL
    if (is_method("CANCEL")) {
        if (t_check_trans()) {
            t_relay();
        }
        exit;
    }

    # Handle retransmissions
    if (!is_method("ACK")) {
        if (t_precheck_trans()) {
            t_check_trans();
            exit;
        }
        t_check_trans();
    }

    # Handle REGISTER
    if (is_method("REGISTER")) {
        xlog("L_INFO", "REGISTER from \$fU@\$fd\\n");

        if (!auth_check("\$fd", "subscriber", "1")) {
            auth_challenge("\$fd", "0");
            exit;
        }

        consume_credentials();

        if (!save("location")) {
            sl_reply_error();
        }
        exit;
    }

    # Handle INVITE - route to FreeSWITCH servers (simple round-robin)
    if (is_method("INVITE")) {
        xlog("L_INFO", "INVITE from \$fU to \$rU\\n");
        record_route();

        # Simple load balancing between FreeSWITCH servers
        # Use hash of call-id to distribute calls
        \$var(server_choice) = core_hash("\$ci", "", "1");

        if (\$var(server_choice) == 0) {
            # Route to FreeSWITCH Server 1
            rewritehostport("$SERVER_003_IP:5060");
            xlog("L_INFO", "Routing to FreeSWITCH Server 1: $SERVER_003_IP:5060\\n");
        } else {
            # Route to FreeSWITCH Server 2
            rewritehostport("$SERVER_004_IP:5060");
            xlog("L_INFO", "Routing to FreeSWITCH Server 2: $SERVER_004_IP:5060\\n");
        }

        # Forward the request
        if (!t_relay()) {
            sl_reply_error();
            exit;
        }
        exit;
    }

    # Handle OPTIONS
    if (is_method("OPTIONS")) {
        sl_send_reply("200", "OK");
        exit;
    }

    # Default response
    sl_send_reply("404", "Not Found");
}
EOF

echo "✓ Kamailio configuration created"
echo ""

echo "10. Configuring firewall..."
# Install ufw if not present
if ! command -v ufw &> /dev/null; then
    echo "Installing ufw firewall..."
    sudo apt install -y ufw
fi

# Configure firewall rules
sudo ufw allow 5060/udp comment "Kamailio SIP"
sudo ufw allow 5432/tcp comment "PostgreSQL"
sudo ufw allow 22/tcp comment "SSH"

# Enable firewall if not already enabled
if ! sudo ufw status | grep -q "Status: active"; then
    echo "y" | sudo ufw enable
fi

echo "✓ Firewall configured"
echo ""

echo "11. Starting services..."
sudo systemctl restart kamailio
sudo systemctl enable kamailio
sleep 3
echo ""

echo "12. Testing configuration..."
if sudo systemctl is-active kamailio > /dev/null; then
    echo "✓ Kamailio is running"

    # Test dispatcher
    echo "Testing dispatcher status..."
    sleep 2
    if [ -p /run/kamailio/kamailio_rpc.fifo ]; then
        echo "dispatcher.list" | sudo tee /run/kamailio/kamailio_rpc.fifo > /dev/null
        sleep 1
    fi

else
    echo "✗ Kamailio failed to start"
    echo "Checking logs..."
    sudo journalctl -u kamailio.service -n 20 --no-pager
    echo ""
    echo "Checking configuration syntax..."
    sudo kamailio -c -f /etc/kamailio/kamailio.cfg
fi

if sudo systemctl is-active <EMAIL> > /dev/null; then
    echo "✓ PostgreSQL is running"
else
    echo "✗ PostgreSQL failed to start"
fi

echo ""
echo "=== Server_002 Setup Complete ==="
echo ""
echo "Kamailio SIP Proxy Configuration:"
echo "  Listen Address: $SERVER_002_IP:5060"
echo "  SIP Domain: $SIP_DOMAIN"
echo "  Database: PostgreSQL on localhost:5432"
echo ""
echo "Test Users Created:"
echo "  1001@$SIP_DOMAIN (password: pass1001)"
echo "  1002@$SIP_DOMAIN (password: pass1002)"
echo "  1003@$SIP_DOMAIN (password: pass1003)"
echo ""
echo "FreeSWITCH Load Balancing:"
echo "  Server 1: $SERVER_003_IP:5060"
echo "  Server 2: $SERVER_004_IP:5060"
echo ""
echo "Next steps:"
echo "1. Setup FreeSWITCH on server_003 and server_004"
echo "2. Test SIP registration with a client"
echo "3. Monitor logs: sudo journalctl -fu kamailio.service"
