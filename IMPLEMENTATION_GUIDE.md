# FreeSWITCH Cluster Implementation Guide

## 🎯 **Main Purpose**
Create a clustered FreeSWITCH setup with <PERSON><PERSON><PERSON> as SIP proxy and load balancer, using PostgreSQL for centralized configuration and user management.

## 🚨 **AWS-Specific Requirements**
This implementation is designed for **AWS EC2 instances** and requires specific Security Group configurations. **Port 5060 must be opened** in AWS Security Groups before SIP clients can connect.

## 🏗️ **Architecture Overview**

```
                    ┌─────────────────┐
                    │   SIP Clients   │
                    │  (1001, 1002,   │
                    │     1003)       │
                    └─────────┬───────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   Server_002    │
                    │  Kamailio SIP   │
                    │ Proxy + Load    │
                    │ Balancer +      │
                    │  PostgreSQL     │
                    │ ***********     │
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │                 │
                    ▼                 ▼
          ┌─────────────────┐ ┌─────────────────┐
          │   Server_003    │ │   Server_004    │
          │  FreeSWITCH     │ │  FreeSWITCH     │
          │ Media Server 1  │ │ Media Server 2  │
          │  ***********    │ │ *************   │
          └─────────────────┘ └─────────────────┘
```

## 📋 **Server Roles**

### **Server_002 (***********)**
- **Role**: SIP Proxy, Load Balancer, Database Server
- **Services**: Ka<PERSON><PERSON>, PostgreSQL 15
- **Purpose**: 
  - Route SIP traffic to FreeSWITCH servers
  - Load balance calls between media servers
  - Store user authentication and configuration
  - Provide centralized database for cluster

### **Server_003 (***********)**
- **Role**: FreeSWITCH Media Server 1
- **Services**: FreeSWITCH
- **Purpose**: 
  - Handle media processing and call control
  - Connect to centralized database on Server_002
  - Process calls routed from Kamailio

### **Server_004 (*************)**
- **Role**: FreeSWITCH Media Server 2
- **Services**: FreeSWITCH
- **Purpose**: 
  - Backup/redundant media processing
  - Load balancing for high call volumes
  - Connect to centralized database on Server_002

## 🚀 **Implementation Steps**

### **Step 0: AWS Security Group Configuration (CRITICAL FIRST STEP)**

**⚠️ MUST BE DONE BEFORE TESTING SIP CLIENTS ⚠️**

Configure AWS Security Groups for each server:

#### **Server_002 (***********) Security Group:**
```
Inbound Rules:
- Type: Custom UDP Rule, Protocol: UDP, Port: 5060, Source: 0.0.0.0/0, Description: SIP Signaling
- Type: Custom TCP Rule, Protocol: TCP, Port: 5060, Source: 0.0.0.0/0, Description: SIP Signaling TCP
- Type: PostgreSQL, Protocol: TCP, Port: 5432, Source: ***********/32, Description: DB from Server_003
- Type: PostgreSQL, Protocol: TCP, Port: 5432, Source: *************/32, Description: DB from Server_004
- Type: SSH, Protocol: TCP, Port: 22, Source: Your-IP/32, Description: SSH Access
```

#### **Server_003/004 Security Groups:**
```
Inbound Rules:
- Type: Custom UDP Rule, Protocol: UDP, Port: 5060, Source: ***********/32, Description: SIP from Kamailio
- Type: Custom UDP Rule, Protocol: UDP, Port: 16384-32768, Source: 0.0.0.0/0, Description: RTP Media
- Type: SSH, Protocol: TCP, Port: 22, Source: Your-IP/32, Description: SSH Access
```

### **Step 1: Setup Server_002 (Kamailio + PostgreSQL)** ✅ **COMPLETED**

```bash
# On Server_002
chmod +x server_002_setup.sh
sudo ./server_002_setup.sh
```

**What this does:**
- Installs and configures PostgreSQL 15
- Creates database and user accounts
- Installs and configures Kamailio
- Sets up SIP proxy with load balancing
- Creates test users (1001, 1002, 1003)
- Configures dispatcher for FreeSWITCH servers
- Auto-detects AWS private IP for binding
- Configures kamctl for database management

**Status**: ✅ **COMPLETED AND WORKING**

### **Step 2: Setup Server_003 (FreeSWITCH Media Server 1)** ⏳ **PENDING**

```bash
# On Server_003
chmod +x server_003_setup.sh
sudo ./server_003_setup.sh
```

**What this does:**
- Installs FreeSWITCH from SignalWire repository
- Configures PostgreSQL connection to Server_002
- Sets up SIP profiles for media handling
- Configures dialplan for call routing
- Creates test extensions and features

**Status**: ⏳ **NOT YET IMPLEMENTED**

### **Step 3: Setup Server_004 (FreeSWITCH Media Server 2)** ⏳ **PENDING**

```bash
# On Server_004
chmod +x server_004_setup.sh
sudo ./server_004_setup.sh
```

**What this does:**
- Installs FreeSWITCH (identical to Server_003)
- Configures PostgreSQL connection to Server_002
- Sets up identical SIP profiles and dialplan
- Provides redundancy and load balancing

**Status**: ⏳ **NOT YET IMPLEMENTED**

### **Step 4: Test the Cluster**

```bash
# On any server
chmod +x test_cluster.sh
./test_cluster.sh
```

**Testing options:**
- `./test_cluster.sh all` - Complete cluster test
- `./test_cluster.sh network` - Network connectivity
- `./test_cluster.sh database` - Database connectivity
- `./test_cluster.sh kamailio` - Kamailio status
- `./test_cluster.sh freeswitch` - FreeSWITCH status
- `./test_cluster.sh summary` - Configuration summary

## 🔧 **Configuration Details**

### **AWS Networking Configuration**
- **Public IPs**: Used by external clients (***********, ***********, *************)
- **Private IPs**: Used by services internally (172.31.x.x range)
- **Kamailio binds to**: Private IP (auto-detected: *************)
- **Clients connect to**: Public IP (AWS NAT handles forwarding)
- **Security Groups**: Must allow port 5060 for SIP traffic

### **Database Configuration**
- **Database**: `freeswitch_db`
- **User**: `fs_user`
- **Password**: `SecurePass2024!`
- **Host**: `***********` (Server_002 public) / `*************` (private)
- **Port**: `5432`

### **SIP Configuration**
- **Domain**: `km1.ethiopiatrips.com`
- **Proxy**: `***********:5060` (public IP for clients)
- **Internal Binding**: `*************:5060` (private IP)
- **Protocol**: UDP
- **Authentication**: Database-based

### **Test Users**
| Username | Domain | Password | Purpose |
|----------|--------|----------|---------|
| 1001 | km1.ethiopiatrips.com | pass1001 | Test user 1 |
| 1002 | km1.ethiopiatrips.com | pass1002 | Test user 2 |
| 1003 | km1.ethiopiatrips.com | pass1003 | Test user 3 |

### **Test Extensions**
| Extension | Purpose |
|-----------|---------|
| 9196 | Echo test |
| 9664 | Hold music test |
| 1001-1019 | Extension to extension calling |
| *97/*98 | Voicemail access |

## 🌐 **SIP Client Configuration**

### **For SIP Phones/Softphones:**
- **Proxy/Registrar**: `***********`
- **Port**: `5060`
- **Transport**: `UDP`
- **Domain**: `km1.ethiopiatrips.com`
- **Username**: `1001` (or 1002, 1003)
- **Password**: `pass1001` (or pass1002, pass1003)

### **For Web Testing:**
Use: https://www.innovateasterisk.com/phone/
- **Server**: `***********:5060`
- **Username**: `1001`
- **Password**: `pass1001`

## 🔍 **Monitoring and Troubleshooting**

### **Service Status Commands**
```bash
# Kamailio (Server_002)
sudo systemctl status kamailio
sudo journalctl -fu kamailio.service

# PostgreSQL (Server_002)
sudo <NAME_EMAIL>
sudo journalctl -fu <EMAIL>

# FreeSWITCH (Server_003, Server_004)
sudo systemctl status freeswitch
sudo journalctl -fu freeswitch.service
sudo fs_cli -x "status"
```

### **Database Queries**
```sql
-- Check registered users
SELECT * FROM location;

-- Check dispatcher destinations
SELECT * FROM dispatcher;

-- Check subscriber accounts
SELECT * FROM subscriber;
```

### **Kamailio Commands**
```bash
# Check dispatcher status
sudo kamctl dispatcher show

# Check user registrations
sudo kamctl ul show

# Check specific database table
sudo kamctl db show subscriber

# Reload dispatcher
sudo kamctl dispatcher reload

# Add new user
sudo <NAME_EMAIL> pass1004

# Remove user
sudo <NAME_EMAIL>
```

## 🔒 **Security Considerations**

1. **AWS Security Groups (Critical)**:
   - Server_002: 5060/udp+tcp (SIP), 5432/tcp (PostgreSQL from cluster only)
   - Server_003/004: 5060/udp (SIP from Kamailio), 16384-32768/udp (RTP)
   - SSH access restricted to your IP only

2. **Database Security**:
   - PostgreSQL configured for specific user access
   - MD5 authentication required
   - Network access restricted to cluster IPs only
   - Superuser privileges removed after setup

3. **SIP Security**:
   - Authentication required for all registrations
   - Sanity checks for malformed SIP messages
   - Rate limiting and hop count validation
   - NAT traversal handled automatically

## 🚨 **Common Issues and Troubleshooting**

### **Issue 1: Port 5060 Connection Timeout**
**Symptoms**: `telnet *********** 5060` hangs, SIP clients can't register
**Cause**: AWS Security Group blocking port 5060
**Fix**: Add UDP/TCP 5060 inbound rules to Server_002 security group

### **Issue 2: kamctl Authentication Errors**
**Symptoms**: `kamctl dispatcher show` fails with "password authentication failed for user kamailioro"
**Cause**: Missing read-only user configuration in kamctlrc
**Fix**: Add to `/etc/kamailio/kamctlrc`:
```bash
DBROUSER=fs_user
DBROPW=SecurePass2024!
```

### **Issue 3: Kamailio Bind Errors**
**Symptoms**: Kamailio fails to start with "Cannot assign requested address"
**Cause**: Trying to bind to public IP instead of private IP
**Fix**: Use private IP (172.31.x.x) in listen directive, not public IP

### **Issue 4: Database Connection Refused**
**Symptoms**: FreeSWITCH can't connect to PostgreSQL
**Cause**: PostgreSQL not configured for network access or wrong IP
**Fix**: Check pg_hba.conf and postgresql.conf for network settings

### **Verification Commands**
```bash
# Test port connectivity
telnet *********** 5060

# Check if Kamailio is listening
sudo ss -tulpn | grep :5060

# Test database connectivity
psql -h *********** -U fs_user -d freeswitch_db -c "SELECT 1;"

# Check AWS private IP
curl -s http://***************/latest/meta-data/local-ipv4
```

## 📊 **Current Implementation Status**

### **✅ Completed (Server_002)**
1. **Kamailio SIP Proxy** - Running and listening on *************:5060
2. **PostgreSQL Database** - Configured with test users and dispatcher destinations
3. **Load Balancer Configuration** - Ready to route to FreeSWITCH servers
4. **Database Management** - kamctl commands working
5. **Test Users Created** - 1001, 1002, 1003 ready for registration

### **⏳ Pending**
1. **AWS Security Groups** - Port 5060 needs to be opened
2. **Server_003 Setup** - FreeSWITCH Media Server 1
3. **Server_004 Setup** - FreeSWITCH Media Server 2
4. **End-to-End Testing** - SIP registration and call routing

### **🎯 Expected Results After Full Implementation**
1. **SIP clients can register** to `***********:5060`
2. **Calls are load balanced** between Server_003 and Server_004
3. **Database stores** all user registrations and call data
4. **High availability** - if one FreeSWITCH server fails, calls route to the other
5. **Centralized management** - all configuration in PostgreSQL database

## 🎯 **Testing Scenarios**

### **Phase 1: Basic SIP Registration (Available Now)**
1. **Prerequisites**: Configure AWS Security Group for port 5060
2. **Registration Test**: Register <EMAIL> to ***********:5060
3. **Verification**: Check registration with `sudo kamctl ul show`

### **Phase 2: Call Testing (After FreeSWITCH Setup)**
1. **Echo Test**: Call 9196 for echo test
2. **Music Test**: Call 9664 for hold music
3. **Extension Test**: Call between 1001 and 1002
4. **Load Balancing**: Multiple simultaneous calls
5. **Failover Test**: Stop one FreeSWITCH server, verify calls still work

### **Immediate Testing Steps**
```bash
# 1. Test port connectivity (should connect immediately)
telnet *********** 5060

# 2. Configure SIP client with:
#    Server: ***********:5060
#    Username: 1001
#    Password: pass1001
#    Domain: km1.ethiopiatrips.com

# 3. Check registration on server
sudo kamctl ul show

# 4. Monitor logs
sudo journalctl -fu kamailio.service
```

## 📞 **Immediate Next Steps**

### **Priority 1: Enable SIP Testing**
1. **Configure AWS Security Group** - Add port 5060 inbound rules to Server_002
2. **Test SIP Registration** - Use softphone or web client <NAME_EMAIL>
3. **Verify Kamailio Logs** - Monitor registration attempts

### **Priority 2: Complete Cluster Setup**
1. **Setup Server_003** - Install and configure FreeSWITCH Media Server 1
2. **Setup Server_004** - Install and configure FreeSWITCH Media Server 2
3. **End-to-End Testing** - Test call routing and load balancing

### **Future Enhancements**
1. **Production Deployment**: Update passwords and security settings
2. **SSL/TLS**: Configure encrypted SIP (SIPS)
3. **Monitoring**: Add Nagios/Zabbix monitoring
4. **Backup**: Configure database backups
5. **Scaling**: Add more FreeSWITCH servers as needed

---

## 🎉 **Quick Start for Testing**

**If you just want to test SIP registration right now:**

1. **Open AWS Security Group** for Server_002 and add:
   - UDP 5060 from 0.0.0.0/0
   - TCP 5060 from 0.0.0.0/0

2. **Test connectivity**:
   ```bash
   telnet *********** 5060
   ```

3. **Configure any SIP client** with:
   - Server: `***********:5060`
   - Username: `1001`
   - Password: `pass1001`
   - Domain: `km1.ethiopiatrips.com`

4. **Verify registration**:
   ```bash
   ssh -i server_002_key admin@*********** "sudo kamctl ul show"
   ```

The Kamailio proxy is ready and waiting for your first SIP registration! 🚀
