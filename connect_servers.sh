#!/bin/bash

# SSH Connection Scripts for All Servers

echo "Available servers:"
echo "1. Server_001 (pbx.ethiopiatrips.com) - Password auth"
echo "2. Server_002 (km1.ethiopiatrips.com) - Key auth"
echo "3. Server_003 (fs1.ethiopiatrips.com) - Key auth"
echo "4. Server_004 (fs2.ethiopiatrips.com) - Key auth"
echo ""

# Function to connect to Server_001
connect_server_001() {
    echo "Connecting to Server_001..."
    echo "IP: **************"
    echo "Domain: pbx.ethiopiatrips.com"
    echo "Username: root"
    echo "Password: id_rsa_pub_parrot"
    echo ""
    ssh root@**************
}

# Function to connect to Server_002
connect_server_002() {
    echo "Connecting to Server_002..."
    echo "IP: ***********"
    echo "Domain: km1.ethiopiatrips.com"
    echo "Username: admin"
    echo ""
    ssh -i server_002_key admin@***********
}

# Function to connect to Server_003
connect_server_003() {
    echo "Connecting to Server_003..."
    echo "IP: ***********"
    echo "Domain: fs1.ethiopiatrips.com"
    echo "Username: admin"
    echo ""
    ssh -i server_003_key admin@***********
}

# Function to connect to Server_004
connect_server_004() {
    echo "Connecting to Server_004..."
    echo "IP: *************"
    echo "Domain: fs2.ethiopiatrips.com"
    echo "Username: admin"
    echo ""
    ssh -i server_004_key admin@*************
}

# Main menu
case "$1" in
    1|server1|001)
        connect_server_001
        ;;
    2|server2|002)
        connect_server_002
        ;;
    3|server3|003)
        connect_server_003
        ;;
    4|server4|004)
        connect_server_004
        ;;
    *)
        echo "Usage: $0 [1|2|3|4]"
        echo "Or run individual connection commands:"
        echo ""
        echo "Server_001: ssh root@**************"
        echo "Server_002: ssh -i server_002_key admin@*************"
        echo "Server_003: ssh -i server_003_key admin@*************"
        echo "Server_004: ssh -i server_004_key admin@**************"
        ;;
esac
