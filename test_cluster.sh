#!/bin/bash
# FreeSWITCH Cluster Testing and Monitoring Script
# Tests the complete Kamailio + FreeSWITCH cluster setup

echo "=== FreeSWITCH Cluster Testing Script ==="
echo ""

# Configuration variables
SERVER_002_IP="***********"   # Kamailio Proxy
SERVER_003_IP="***********"   # FreeSWITCH Server 1
SERVER_004_IP="*************" # FreeSWITCH Server 2
SIP_DOMAIN="km1.ethiopiatrips.com"
PG_DB="freeswitch_db"
PG_USER="fs_user"
PG_PASS="SecurePass2024!"

# Determine which server we're on
CURRENT_IP=$(hostname -I | awk '{print $1}')
if [[ "$CURRENT_IP" == *"*************"* ]]; then
    CURRENT_SERVER="server_002"
    CURRENT_PUBLIC_IP="$SERVER_002_IP"
elif [[ "$CURRENT_IP" == *"***********"* ]]; then
    CURRENT_SERVER="server_003"
    CURRENT_PUBLIC_IP="$SERVER_003_IP"
elif [[ "$CURRENT_IP" == *"*************"* ]]; then
    CURRENT_SERVER="server_004"
    CURRENT_PUBLIC_IP="$SERVER_004_IP"
else
    CURRENT_SERVER="unknown"
    CURRENT_PUBLIC_IP="unknown"
fi

echo "Current server: $CURRENT_SERVER ($CURRENT_PUBLIC_IP)"
echo ""

function test_network_connectivity() {
    echo "=== Network Connectivity Tests ==="
    
    echo "Testing connectivity to all servers..."
    for server in $SERVER_002_IP $SERVER_003_IP $SERVER_004_IP; do
        if ping -c 2 $server > /dev/null 2>&1; then
            echo "✓ $server - reachable"
        else
            echo "✗ $server - unreachable"
        fi
    done
    echo ""
}

function test_database_connectivity() {
    echo "=== Database Connectivity Tests ==="

    export PGPASSWORD="$PG_PASS"

    # Use localhost if we're on server_002, otherwise use public IP
    if [[ "$CURRENT_SERVER" == "server_002" ]]; then
        DB_HOST="localhost"
    else
        DB_HOST="$SERVER_002_IP"
    fi

    if psql -h $DB_HOST -U $PG_USER -d $PG_DB -c "SELECT version();" > /dev/null 2>&1; then
        echo "✓ PostgreSQL connection successful"
        
        echo "Database tables:"
        psql -h $DB_HOST -U $PG_USER -d $PG_DB -c "
        SELECT
            schemaname,
            tablename,
            tableowner
        FROM pg_tables
        WHERE schemaname = 'public'
        ORDER BY tablename;"

        echo ""
        echo "Subscriber count:"
        psql -h $DB_HOST -U $PG_USER -d $PG_DB -c "SELECT COUNT(*) as users FROM subscriber;"

        echo ""
        echo "Dispatcher destinations:"
        psql -h $DB_HOST -U $PG_USER -d $PG_DB -c "SELECT setid, destination, flags, description FROM dispatcher ORDER BY setid, priority;"
        
    else
        echo "✗ PostgreSQL connection failed"
    fi
    echo ""
}

function test_kamailio_status() {
    echo "=== Kamailio Status Tests ==="
    
    if [[ "$CURRENT_SERVER" == "server_002" ]]; then
        echo "Testing Kamailio on local server..."
        
        if sudo systemctl is-active kamailio > /dev/null; then
            echo "✓ Kamailio service is running"
        else
            echo "✗ Kamailio service is not running"
            sudo systemctl status kamailio --no-pager -l
        fi
        
        # Test FIFO interface
        if [ -p /run/kamailio/kamailio_rpc.fifo ]; then
            echo "✓ Kamailio FIFO interface available"
            
            echo "Dispatcher status:"
            echo "kamcmd dispatcher.list" | sudo tee /run/kamailio/kamailio_rpc.fifo > /dev/null
            sleep 1
            if [ -f /run/kamailio/kamailio_rpc.fifo.out ]; then
                cat /run/kamailio/kamailio_rpc.fifo.out
                rm -f /run/kamailio/kamailio_rpc.fifo.out
            fi
        else
            echo "✗ Kamailio FIFO interface not available"
        fi
        
        # Test SIP port (use ss instead of netstat)
        if sudo ss -tulpn | grep ":5060" > /dev/null; then
            echo "✓ Kamailio listening on port 5060"
            # Show what IP it's bound to
            echo "  Binding details:"
            sudo ss -tulpn | grep ":5060" | sed 's/^/    /'
        else
            echo "✗ Kamailio not listening on port 5060"
        fi
        
    else
        echo "Testing Kamailio on remote server ($SERVER_002_IP)..."
        
        # Test SIP connectivity
        if nc -z -u $SERVER_002_IP 5060 2>/dev/null; then
            echo "✓ Kamailio SIP port reachable"
        else
            echo "✗ Kamailio SIP port not reachable"
        fi
    fi
    echo ""
}

function test_freeswitch_status() {
    echo "=== FreeSWITCH Status Tests ==="
    
    if [[ "$CURRENT_SERVER" == "server_003" ]] || [[ "$CURRENT_SERVER" == "server_004" ]]; then
        echo "Testing FreeSWITCH on local server..."
        
        if sudo systemctl is-active freeswitch > /dev/null; then
            echo "✓ FreeSWITCH service is running"
        else
            echo "✗ FreeSWITCH service is not running"
            sudo systemctl status freeswitch --no-pager -l
        fi
        
        # Test FreeSWITCH CLI
        if command -v fs_cli > /dev/null; then
            echo "✓ FreeSWITCH CLI available"
            
            echo "FreeSWITCH status:"
            sudo fs_cli -x "status" 2>/dev/null || echo "Could not connect to FreeSWITCH"
            
            echo ""
            echo "SIP profiles:"
            sudo fs_cli -x "sofia status" 2>/dev/null || echo "Could not get SIP status"
            
        else
            echo "✗ FreeSWITCH CLI not available"
        fi
        
        # Test SIP port (use ss instead of netstat)
        if sudo ss -tulpn | grep ":5060" > /dev/null; then
            echo "✓ FreeSWITCH listening on port 5060"
        else
            echo "✗ FreeSWITCH not listening on port 5060"
        fi
        
    else
        echo "Testing FreeSWITCH servers remotely..."
        
        for server in $SERVER_003_IP $SERVER_004_IP; do
            if nc -z -u $server 5060 2>/dev/null; then
                echo "✓ FreeSWITCH on $server - SIP port reachable"
            else
                echo "✗ FreeSWITCH on $server - SIP port not reachable"
            fi
        done
    fi
    echo ""
}

function test_sip_registration() {
    echo "=== SIP Registration Tests ==="
    
    if command -v sipsak > /dev/null; then
        echo "Testing SIP OPTIONS to Kamailio..."
        if sipsak -s sip:$SERVER_002_IP:5060 > /dev/null 2>&1; then
            echo "✓ SIP OPTIONS successful"
        else
            echo "✗ SIP OPTIONS failed"
        fi
    else
        echo "⚠ sipsak not available for SIP testing"
        echo "Install with: sudo apt install sipsak"
    fi
    echo ""
}

function show_logs() {
    echo "=== Recent Log Entries ==="
    
    if [[ "$CURRENT_SERVER" == "server_002" ]]; then
        echo "Kamailio logs (last 10 lines):"
        sudo journalctl -u kamailio.service -n 10 --no-pager
    elif [[ "$CURRENT_SERVER" == "server_003" ]] || [[ "$CURRENT_SERVER" == "server_004" ]]; then
        echo "FreeSWITCH logs (last 10 lines):"
        sudo journalctl -u freeswitch.service -n 10 --no-pager
    fi
    echo ""
}

function show_cluster_summary() {
    echo "=== Cluster Summary ==="
    echo ""
    echo "Architecture:"
    echo "  Server_002 ($SERVER_002_IP): Kamailio SIP Proxy + PostgreSQL Database"
    echo "  Server_003 ($SERVER_003_IP): FreeSWITCH Media Server 1"
    echo "  Server_004 ($SERVER_004_IP): FreeSWITCH Media Server 2"
    echo ""
    echo "SIP Domain: $SIP_DOMAIN"
    echo "Database: $PG_DB on $SERVER_002_IP"
    echo ""
    echo "Test Users:"
    echo "  1001@$SIP_DOMAIN (password: pass1001)"
    echo "  1002@$SIP_DOMAIN (password: pass1002)"
    echo "  1003@$SIP_DOMAIN (password: pass1003)"
    echo ""
    echo "Test Extensions:"
    echo "  9196 - Echo test"
    echo "  9664 - Hold music test"
    echo "  1001-1019 - Extension to extension calling"
    echo "  *97/*98 - Voicemail access"
    echo ""
    echo "SIP Client Configuration:"
    echo "  Proxy: $SERVER_002_IP:5060"
    echo "  Domain: $SIP_DOMAIN"
    echo "  Username: 1001 (or 1002, 1003)"
    echo "  Password: pass1001 (or pass1002, pass1003)"
    echo ""
    echo "Web Testing:"
    echo "  Use: https://www.innovateasterisk.com/phone/"
    echo "  Server: $SERVER_002_IP:5060"
    echo "  Username: 1001"
    echo "  Password: pass1001"
    echo ""
}

# Main execution
case "${1:-all}" in
    "network")
        test_network_connectivity
        ;;
    "database")
        test_database_connectivity
        ;;
    "kamailio")
        test_kamailio_status
        ;;
    "freeswitch")
        test_freeswitch_status
        ;;
    "sip")
        test_sip_registration
        ;;
    "logs")
        show_logs
        ;;
    "summary")
        show_cluster_summary
        ;;
    "all")
        test_network_connectivity
        test_database_connectivity
        test_kamailio_status
        test_freeswitch_status
        test_sip_registration
        show_logs
        show_cluster_summary
        ;;
    *)
        echo "Usage: $0 [network|database|kamailio|freeswitch|sip|logs|summary|all]"
        echo ""
        echo "Options:"
        echo "  network    - Test network connectivity between servers"
        echo "  database   - Test PostgreSQL database connectivity"
        echo "  kamailio   - Test Kamailio SIP proxy status"
        echo "  freeswitch - Test FreeSWITCH media server status"
        echo "  sip        - Test SIP registration and connectivity"
        echo "  logs       - Show recent log entries"
        echo "  summary    - Show cluster configuration summary"
        echo "  all        - Run all tests (default)"
        ;;
esac
