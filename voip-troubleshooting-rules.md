# VoIP Infrastructure Troubleshooting Rules

## Overview

This document provides standardized rules and procedures for troubleshooting VoIP infrastructure issues during setup and maintenance tasks. Follow these rules systematically when encountering implementation problems with FreeSWITCH, Kamailio, PostgreSQL, and related VoIP technologies.

## Rule 1: Documentation Reference Protocol

### When to Apply
- Encountering implementation issues with VoIP components
- Uncertain about configuration parameters or syntax
- Need authoritative guidance on best practices
- Troubleshooting complex integration problems

### Procedure
1. **Always use context7 tools** for official documentation lookup
2. **First**: Use `resolve-library-id_npx` to find the correct library identifier
3. **Then**: Use `get-library-docs_npx` with the resolved library ID

### Examples

#### FreeSWITCH Documentation Lookup
```bash
# Step 1: Resolve FreeSWITCH library ID
resolve-library-id_npx("FreeSWITCH")

# Step 2: Get specific documentation (example with resolved ID)
get-library-docs_npx("/freeswitch/freeswitch", topic="configuration")
```

#### Kamailio Documentation Lookup
```bash
# Step 1: Resolve Kamailio library ID
resolve-library-id_npx("Kamailio")

# Step 2: Get dispatcher module documentation
get-library-docs_npx("/kamailio/kamailio", topic="dispatcher module")
```

#### PostgreSQL Documentation Lookup
```bash
# Step 1: Resolve PostgreSQL library ID
resolve-library-id_npx("PostgreSQL")

# Step 2: Get database administration documentation
get-library-docs_npx("/postgresql/postgresql", topic="database administration")
```

### Key Points
- **Never guess** configuration syntax - always verify with official docs
- **Use specific topics** when calling get-library-docs_npx for targeted information
- **Cross-reference** multiple sources when dealing with integration issues

## Rule 2: Command Execution Standards

### When to Apply
- All system configuration changes
- Service management operations
- File modifications and permissions
- Package installations and updates

### Procedure
**Always execute commands with sudo privileges**

### Examples

#### Service Management
```bash
# Correct approach
sudo systemctl start kamailio
sudo systemctl stop freeswitch
sudo systemctl restart postgresql
sudo systemctl status kamailio.service

# Check service logs
sudo journalctl -xeu kamailio.service
sudo journalctl -xeu freeswitch.service
```

#### Configuration File Editing
```bash
# Correct approach
sudo nano /etc/kamailio/kamailio.cfg
sudo vim /etc/freeswitch/freeswitch.xml
sudo chown freeswitch:freeswitch /etc/freeswitch/autoload_configs/
```

#### Database Operations
```bash
# Correct approach
sudo -u postgres psql kamailio
sudo -u postgres createdb freeswitch_cluster
sudo -u postgres pg_dump kamailio > backup.sql
```

#### Package Management
```bash
# Correct approach
sudo apt update
sudo apt install kamailio kamailio-postgres-modules
sudo apt install freeswitch-meta-all
```

### Key Points
- **Never run** system commands without sudo when administrative access is required
- **Always verify** permissions before and after configuration changes
- **Use appropriate user context** (e.g., -u postgres for PostgreSQL operations)

## Rule 3: Server Access Protocol

### When to Apply
- Accessing remote VoIP cluster servers
- Performing maintenance on distributed components
- Troubleshooting cross-server connectivity issues

### Infrastructure Overview
- **Server_002**: km1.ethiopiatrips.com (Kamailio Load Balancer + PostgreSQL)
- **Server_003**: fs1.ethiopiatrips.com (FreeSWITCH Node 1)
- **Server_004**: fs2.ethiopiatrips.com (FreeSWITCH Node 2)

### Procedure
**Always use the connect_servers.sh script for SSH access**

### Examples

#### Connecting to Kamailio Server
```bash
# Use the standardized connection script
./connect_servers.sh
# Select option 2 for Server_002 (km1.ethiopiatrips.com)
```

#### Connecting to FreeSWITCH Nodes
```bash
# For FreeSWITCH Node 1
./connect_servers.sh
# Select option 3 for Server_003 (fs1.ethiopiatrips.com)

# For FreeSWITCH Node 2
./connect_servers.sh
# Select option 4 for Server_004 (fs2.ethiopiatrips.com)
```

#### Multi-Server Operations
```bash
# When troubleshooting cluster-wide issues:
# 1. Connect to each server in sequence
# 2. Verify service status on each node
# 3. Check inter-server connectivity
# 4. Validate database connections from all nodes
```

### Key Points
- **Always use** the connect_servers.sh script for consistency
- **Maintain separate SSH sessions** when working across multiple servers
- **Document server-specific configurations** and their relationships
- **Verify network connectivity** between cluster nodes

## Rule 4: Error Resolution Process

### When to Apply
- Service startup failures
- Configuration validation errors
- Database connection issues
- Module loading problems

### Systematic Procedure

#### Step 1: Check Service Logs
```bash
# Always start with service logs
sudo systemctl status [service-name].service
sudo journalctl -xeu [service-name].service

# For detailed logs
sudo journalctl -f -u [service-name].service
```

#### Step 2: Consult Official Documentation
```bash
# Use context7 tools for authoritative guidance
resolve-library-id_npx("[technology-name]")
get-library-docs_npx("[resolved-id]", topic="[specific-issue]")
```

#### Step 3: Apply Fixes with Proper Permissions
```bash
# Always use sudo for system modifications
sudo [fix-command]
sudo systemctl restart [service-name]
sudo systemctl status [service-name].service
```

### Example: Kamailio Dispatcher Module Error

#### Problem
```
ERROR: dispatcher [dispatch.c:997]: ds_init_db(): invalid table version
```

#### Resolution Process
```bash
# Step 1: Check logs
sudo journalctl -xeu kamailio.service

# Step 2: Consult documentation
resolve-library-id_npx("Kamailio")
get-library-docs_npx("/kamailio/kamailio", topic="dispatcher module database")

# Step 3: Apply fix
sudo -u postgres psql kamailio -c "INSERT INTO version (table_name, table_version) VALUES ('dispatcher', 4);"
sudo systemctl restart kamailio
sudo systemctl status kamailio.service
```

### Key Points
- **Follow the sequence**: Logs → Documentation → Fix → Verify
- **Always verify fixes** by checking service status after changes
- **Document solutions** for future reference
- **Test thoroughly** after implementing fixes

## Best Practices Summary

1. **Documentation First**: Always consult official docs via context7 tools before implementing solutions
2. **Sudo Always**: Use sudo for all system-level operations and configurations
3. **Standardized Access**: Use connect_servers.sh for all SSH connections to cluster servers
4. **Systematic Troubleshooting**: Follow the 4-step error resolution process consistently
5. **Verify Changes**: Always check service status and logs after making modifications
6. **Cross-Reference**: When dealing with integration issues, consult documentation for all involved components

## Emergency Contacts and Resources

- **Official Documentation**: Use context7 tools (resolve-library-id_npx, get-library-docs_npx)
- **Service Logs**: /var/log/syslog, journalctl commands
- **Configuration Paths**:
  - Kamailio: /etc/kamailio/
  - FreeSWITCH: /etc/freeswitch/
  - PostgreSQL: /etc/postgresql/
