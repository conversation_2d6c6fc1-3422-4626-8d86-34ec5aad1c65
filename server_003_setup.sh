#!/bin/bash
# Server_003 Setup: FreeSWITCH Media Server 1
# Based on FreeSWITCH documentation best practices

echo "=== Server_003 Setup: FreeSWITCH Media Server 1 ==="
echo "Purpose: Media Processing and Call Handling"
echo ""

# Configuration variables
SERVER_002_IP="***********"   # Kamailio Proxy
SERVER_003_IP="***********"   # This server
SERVER_004_IP="*************" # FreeSWITCH Server 2
SIP_DOMAIN="km1.ethiopiatrips.com"
PG_DB="freeswitch_db"
PG_USER="fs_user"
PG_PASS="SecurePass2024!"

echo "Configuration:"
echo "  Server IP: $SERVER_003_IP"
echo "  Kamailio Proxy: $SERVER_002_IP"
echo "  SIP Domain: $SIP_DOMAIN"
echo "  Database: $PG_DB on $SERVER_002_IP"
echo ""

echo "1. Updating system packages..."
sudo apt update && sudo apt upgrade -y
echo ""

echo "2. Installing FreeSWITCH dependencies..."
sudo apt install -y wget gnupg2 software-properties-common
sudo apt install -y libpq-dev postgresql-client
echo ""

echo "3. Adding FreeSWITCH repository..."
wget --http-user=signalwire --http-password=pat_NjM2NzE2ZWEtZjJmNi00MzVhLWJkNzMtMGI3YWY0ZjA5NzE2 \
  -O /usr/share/keyrings/signalwire-freeswitch-repo.gpg \
  https://freeswitch.signalwire.com/repo/deb/debian-release/signalwire-freeswitch-repo.gpg

echo "machine freeswitch.signalwire.com login signalwire password pat_NjM2NzE2ZWEtZjJmNi00MzVhLWJkNzMtMGI3YWY0ZjA5NzE2" > ~/.netrc
chmod 600 ~/.netrc

echo "deb [signed-by=/usr/share/keyrings/signalwire-freeswitch-repo.gpg] https://freeswitch.signalwire.com/repo/deb/debian-release/ `lsb_release -sc` main" > /etc/apt/sources.list.d/freeswitch.list
echo "deb-src [signed-by=/usr/share/keyrings/signalwire-freeswitch-repo.gpg] https://freeswitch.signalwire.com/repo/deb/debian-release/ `lsb_release -sc` main" >> /etc/apt/sources.list.d/freeswitch.list

sudo apt update
echo ""

echo "4. Installing FreeSWITCH..."
sudo apt install -y freeswitch-meta-all
echo ""

echo "5. Configuring FreeSWITCH for PostgreSQL..."
# Configure database connection
sudo tee /etc/freeswitch/autoload_configs/switch.conf.xml << EOF
<configuration name="switch.conf" description="Core Configuration">
  <settings>
    <param name="colorize-console" value="true"/>
    <param name="loglevel" value="info"/>
    <param name="switchname" value="fs_server_003"/>
    <param name="core-db-dsn" value="pgsql://host=$SERVER_002_IP dbname=$PG_DB user=$PG_USER password='$PG_PASS' options='-c client_min_messages=NOTICE' application_name='freeswitch_003'"/>
    <param name="max-sessions" value="1000"/>
    <param name="sessions-per-second" value="100"/>
  </settings>
</configuration>
EOF

echo "✓ Database connection configured"
echo ""

echo "6. Configuring SIP profiles..."
# Configure internal SIP profile
sudo tee /etc/freeswitch/sip_profiles/internal.xml << EOF
<profile name="internal">
  <gateways>
  </gateways>
  <domains>
    <domain name="all" alias="false" parse="true"/>
  </domains>
  <settings>
    <param name="debug" value="0"/>
    <param name="sip-trace" value="no"/>
    <param name="sip-capture" value="no"/>
    <param name="rfc2833-pt" value="101"/>
    <param name="sip-port" value="5060"/>
    <param name="dialplan" value="XML"/>
    <param name="context" value="default"/>
    <param name="dtmf-duration" value="2000"/>
    <param name="inbound-codec-prefs" value="OPUS,G722,PCMU,PCMA,GSM"/>
    <param name="outbound-codec-prefs" value="OPUS,G722,PCMU,PCMA,GSM"/>
    <param name="rtp-timer-name" value="soft"/>
    <param name="local-network-acl" value="localnet.auto"/>
    <param name="manage-presence" value="false"/>
    <param name="inbound-codec-negotiation" value="generous"/>
    <param name="nonce-ttl" value="60"/>
    <param name="auth-calls" value="false"/>
    <param name="inbound-late-negotiation" value="true"/>
    <param name="inbound-zrtp-passthru" value="true"/>
    <param name="rtp-ip" value="$SERVER_003_IP"/>
    <param name="sip-ip" value="$SERVER_003_IP"/>
    <param name="ext-rtp-ip" value="$SERVER_003_IP"/>
    <param name="ext-sip-ip" value="$SERVER_003_IP"/>
    <param name="rtp-timeout-sec" value="300"/>
    <param name="rtp-hold-timeout-sec" value="1800"/>
    <param name="enable-100rel" value="true"/>
    <param name="disable-transfer" value="false"/>
    <param name="manual-redirect" value="true"/>
    <param name="enable-compact-headers" value="true"/>
    <param name="enable-timer" value="false"/>
    <param name="minimum-session-expires" value="120"/>
    <param name="apply-nat-acl" value="nat.auto"/>
    <param name="apply-inbound-acl" value="domains"/>
    <param name="record-path" value="/var/lib/freeswitch/recordings"/>
    <param name="record-template" value="\${caller_id_number}.\${target_domain}.\${strftime(%Y-%m-%d-%H-%M-%S)}.wav"/>
  </settings>
</profile>
EOF

echo "✓ Internal SIP profile configured"
echo ""

echo "7. Configuring dialplan..."
sudo tee /etc/freeswitch/dialplan/default.xml << EOF
<?xml version="1.0" encoding="utf-8"?>
<include>
  <context name="default">
    
    <!-- Test extension for echo -->
    <extension name="echo">
      <condition field="destination_number" expression="^9196\$">
        <action application="answer"/>
        <action application="echo"/>
      </condition>
    </extension>
    
    <!-- Test extension for hold music -->
    <extension name="hold_music">
      <condition field="destination_number" expression="^9664\$">
        <action application="answer"/>
        <action application="playback" data="local_stream://moh"/>
      </condition>
    </extension>
    
    <!-- Extension to extension calling -->
    <extension name="Local_Extension">
      <condition field="destination_number" expression="^(10[01][0-9])\$">
        <action application="export" data="dialed_extension=\$1"/>
        <action application="bind_meta_app" data="1 b s execute_extension::dx XML features"/>
        <action application="bind_meta_app" data="2 b s record_session::\$\${recordings_dir}/\$\${caller_id_number}.\$\${strftime(%Y-%m-%d-%H-%M-%S)}.wav"/>
        <action application="bind_meta_app" data="3 b s execute_extension::cf XML features"/>
        <action application="set" data="ringback=\$\${us-ring}"/>
        <action application="set" data="transfer_ringback=\$\${us-ring}"/>
        <action application="set" data="call_timeout=30"/>
        <action application="set" data="hangup_after_bridge=true"/>
        <action application="set" data="continue_on_fail=true"/>
        <action application="bridge" data="user/\${dialed_extension}@\$\${domain}"/>
        <action application="answer"/>
        <action application="sleep" data="1000"/>
        <action application="bridge" data="loopback/app=voicemail:default \$\${domain} \${dialed_extension}"/>
      </condition>
    </extension>
    
    <!-- Voicemail access -->
    <extension name="vmain">
      <condition field="destination_number" expression="^vmain\$|^\*97\$|^\*98\$">
        <action application="answer"/>
        <action application="sleep" data="1000"/>
        <action application="voicemail" data="check default \$\${domain} \$\${caller_id_number}"/>
      </condition>
    </extension>
    
    <!-- Default catch-all -->
    <extension name="unallocated">
      <condition field="destination_number" expression="^.*\$">
        <action application="answer"/>
        <action application="playback" data="ivr/ivr-no_route_destination.wav"/>
        <action application="hangup"/>
      </condition>
    </extension>
    
  </context>
</include>
EOF

echo "✓ Dialplan configured"
echo ""

echo "8. Configuring firewall..."
sudo ufw allow 5060/udp
sudo ufw allow 16384:32768/udp  # RTP range
echo ""

echo "9. Starting FreeSWITCH..."
sudo systemctl enable freeswitch
sudo systemctl start freeswitch
echo ""

echo "10. Testing configuration..."
sleep 5
if sudo systemctl is-active freeswitch > /dev/null; then
    echo "✓ FreeSWITCH is running"
else
    echo "✗ FreeSWITCH failed to start"
    sudo journalctl -u freeswitch.service -n 10 --no-pager
fi

# Test database connection
export PGPASSWORD="$PG_PASS"
if psql -h $SERVER_002_IP -U $PG_USER -d $PG_DB -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✓ Database connection successful"
else
    echo "✗ Database connection failed"
fi

echo ""
echo "=== Server_003 Setup Complete ==="
echo ""
echo "FreeSWITCH Media Server Configuration:"
echo "  Server IP: $SERVER_003_IP"
echo "  SIP Port: 5060"
echo "  RTP Range: 16384-32768"
echo "  Database: $PG_DB on $SERVER_002_IP"
echo ""
echo "Test Extensions:"
echo "  9196 - Echo test"
echo "  9664 - Hold music test"
echo "  1001-1019 - Extension to extension calling"
echo "  *97/*98 - Voicemail access"
echo ""
echo "Next steps:"
echo "1. Setup server_004 (FreeSWITCH Server 2)"
echo "2. Test calls through Kamailio proxy"
echo "3. Monitor logs: sudo fs_cli -x 'console loglevel debug'"
